const AWS = require('aws-sdk');

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const cognito = new AWS.CognitoIdentityServiceProvider(awsConfig);

const USER_POOL_ID = process.env.USER_POOL_ID;

// Helper function to generate policy
const generatePolicy = (principalId, effect, resource, context = {}) => {
    const authResponse = {
        principalId: principalId
    };

    if (effect && resource) {
        const policyDocument = {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: effect,
                    Resource: resource
                }
            ]
        };
        authResponse.policyDocument = policyDocument;
    }

    // Add context to be passed to the lambda function
    if (Object.keys(context).length > 0) {
        authResponse.context = context;
    }

    return authResponse;
};

// Main authorizer handler
exports.handler = async (event) => {
    console.log('Authorizer event:', JSON.stringify(event, null, 2));

    try {
        // Extract token from the authorization header
        const token = event.authorizationToken;

        if (!token) {
            console.log('No authorization token provided');
            throw new Error('Unauthorized');
        }

        // Remove 'Bearer ' prefix if present
        const accessToken = token.replace(/^Bearer\s+/, '');

        if (!accessToken) {
            console.log('No access token found after removing Bearer prefix');
            throw new Error('Unauthorized');
        }

        try {
            // Validate token with Cognito
            const getUserParams = {
                AccessToken: accessToken
            };

            const cognitoUser = await cognito.getUser(getUserParams).promise();
            console.log('Cognito user:', cognitoUser);

            // Extract user information from Cognito attributes
            const userAttributes = {};
            cognitoUser.UserAttributes.forEach(attr => {
                userAttributes[attr.Name] = attr.Value;
            });

            // Create context to pass to the lambda function using Cognito data
            const context = {
                userId: userAttributes.sub, // Use Cognito sub as the primary user ID
                email: userAttributes.email,
                username: cognitoUser.Username,
                cognitoUserId: cognitoUser.Username,
                sub: userAttributes.sub,
                firstName: userAttributes.given_name,
                lastName: userAttributes.family_name
            };

            console.log('User context:', context);

            // Generate allow policy
            return generatePolicy(userAttributes.sub, 'Allow', event.methodArn, context);

        } catch (cognitoError) {
            console.error('Token validation error:', cognitoError);
            throw new Error('Unauthorized');
        }

    } catch (error) {
        console.error('Authorizer error:', error);

        // Return deny policy for any error
        return generatePolicy('user', 'Deny', event.methodArn);
    }
};
